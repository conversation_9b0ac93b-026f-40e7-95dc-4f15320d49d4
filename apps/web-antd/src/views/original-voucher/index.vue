<script setup lang="ts">
  import { computed, ref, watch } from 'vue';

  import { message } from 'ant-design-vue';

  import { useVbenForm } from '#/adapter/form';
  import { useVbenVxeGrid } from '#/adapter/vxe-table';

  import {
    bankColumns,
    bankQuerySchema,
    invoiceColumns,
    invoiceQuerySchema,
  } from './data';

  const tabList = [
    { key: 'output', label: '销项发票' },
    { key: 'input', label: '进项发票' },
    { key: 'bank', label: '银行回单' },
  ];
  const activeTab = ref('output');

  // 动态schema和columns
  const formSchema = computed(() => {
    if (activeTab.value === 'bank') return bankQuerySchema();
    return invoiceQuerySchema();
  });
  const tableColumns = computed(() => {
    if (activeTab.value === 'bank') return bankColumns;
    return invoiceColumns;
  });

  // 数据源
  const tableData = ref([]);
  const loading = ref(false);

  // 用于强制刷新表单和表格
  const formKey = computed(() => `${activeTab.value}-form`);
  const tableKey = computed(() => `${activeTab.value}-table`);

  // 筛选表单
  const [BasicForm, formApi] = useVbenForm({
    schema: formSchema.value,
    wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  });

  // 表格
  const [BasicTable] = useVbenVxeGrid({
    columns: tableColumns.value,
    data: tableData,
    id: 'original-voucher-table',
    loading,
    pagerConfig: { enabled: true },
    rowConfig: { keyField: '_id' },
  } as any);

  // 查询方法
  async function fetchData() {
    loading.value = true;
    try {
      const values = await formApi.getValues();
      let url = '';
      let params: any = {};
      if (activeTab.value === 'bank') {
        url = '/api/bank_receipts/list';
        const [begin_time, end_time] = values.dateRange || [];
        params = {
          begin_time,
          company_name: values.company_name,
          end_time,
          month: values.month,
          type: values.type,
          voucher_number: values.voucher_number,
        };
      } else {
        url = '/api/invoice/list';
        const [begin_time, end_time] = values.dateRange || [];
        params = {
          begin_time,
          company_name: values.company_name,
          end_time,
          input_output: activeTab.value,
          status: values.status,
          voucher_number: values.voucher_number,
        };
      }
      // TODO: 替换为实际请求方法
      const res = await fetch(
        `${url}?${new URLSearchParams(params).toString()}`,
      );
      const json = await res.json();
      if (json.status === 'success') {
        tableData.value = json.data || [];
      } else {
        message.error(json.message || '获取数据失败');
      }
    } catch {
      message.error('请求失败');
    } finally {
      loading.value = false;
    }
  }

  // tab切换/筛选自动刷新
  watch(activeTab, () => {
    // 强制刷新表单和表格
    // 由于key变化，组件会重建，schema/columns会自动生效
    fetchData();
  });

  function handleSearch() {
    fetchData();
  }

  // 操作按钮
  function handleAdd() {
    message.info('新增加原始凭证功能开发中');
  }
  function handleAI() {
    message.info('AI记账功能开发中');
  }
</script>

<template>
  <div class="original-voucher-page">
    <a-tabs v-model:active-key="activeTab">
      <a-tab-pane v-for="tab in tabList" :key="tab.key" :tab="tab.label" />
    </a-tabs>
    <div class="mb-4 flex items-center gap-2">
      <BasicForm :key="formKey" class="flex-1" />
      <a-button type="primary" @click="handleSearch">搜索</a-button>
      <a-button @click="handleAdd">新增加原始凭证</a-button>
      <a-button @click="handleAI">AI记账</a-button>
    </div>
    <BasicTable :key="tableKey">
      <template #action="{ row }">
        <a :href="row.url || '#'" target="_blank">查看原文件</a>
      </template>
    </BasicTable>
  </div>
</template>

<style scoped>
  .original-voucher-page {
    padding: 24px;
    background: #fff;
    border-radius: 8px;
  }
</style>
